# MultiAgentPPT：简单示例用于生成PPT正文

本项目结合了 **A2A 框架** 与 **Google ADK 架构**，通过 SSE（Server-Sent Events）生成结构化的 XML 格式 PPT 内容示例，这个用于测试。

---

## ✨ 项目亮点

* 🔗 **A2A + ADK 无缝集成**：实现多智能体任务执行。
* 📄 **标准化 XML PPT 输出**：支持基于结构化大纲生成内容丰富、布局多样的 XML 演示文稿。
* 🌐 **流式响应支持（SSE）**：支持客户端实时接收 Agent 执行状态及内容更新。

---

## 📂 项目结构说明

| 文件                      | 说明                                |
| ----------------------- | --------------------------------- |
| `a2a_client.py`         | A2A 客户端封装，管理与服务端的交互               |
| `agent.py`              | 主控制 Agent，调度执行任务                  |
| `main_api.py`           | FastAPI 主入口，暴露 SSE 接口             |
| `adk_agent_executor.py` | 实现 A2A 与 ADK 框架的集成逻辑              |
| `.env`                  | 环境变量配置，需包含 Google GenAI API 密钥等信息 |

---

## 📌 示例：主题生成任务

以下是可以测试的典型主题输入，系统将自动生成结构化内容：

* 电动汽车市场调研

---

## 运行方法
```
python main_api.py
```