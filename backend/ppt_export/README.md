# PPT导出服务

这是一个使用Python和python-pptx库的PPT导出服务，用于将演示文稿数据导出为PowerPoint文件。

## 功能特性

- 支持将JSON格式的演示文稿数据转换为PPTX文件
- 自动处理标题页和内容页
- 支持文本、列表等基本元素
- 提供RESTful API接口
- 支持中文内容

## 安装依赖

```bash
# 进入服务目录
cd backend/ppt_export

# 安装Python依赖
pip install -r requirements.txt
```

## 启动服务

### 方式1：使用启动脚本
```bash
./start.sh
```

### 方式2：直接运行
```bash
python3 main.py
```

服务将在 `http://localhost:8001` 启动。

## API接口

### 健康检查
```
GET /
```

### 导出PPT
```
POST /export
Content-Type: application/json

{
  "title": "演示文稿标题",
  "slides": [
    {
      "content": [
        {
          "type": "h1",
          "children": [{"text": "幻灯片标题"}]
        },
        {
          "type": "p",
          "children": [{"text": "段落内容"}]
        },
        {
          "type": "ul",
          "children": [
            {
              "type": "li",
              "children": [
                {
                  "children": [{"text": "列表项1"}]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

响应：返回PPTX文件的二进制数据

## 环境变量

- `PORT`: 服务端口，默认8001

## 日志

服务会输出详细的日志信息，包括：
- 服务启动信息
- 请求处理过程
- 错误信息

## 注意事项

1. 确保Python版本 >= 3.7
2. 建议在虚拟环境中运行
3. 服务需要与前端应用配合使用
4. 导出的PPT文件支持中文内容
