2025/07/03 14:18:17 - INFO - google_adk.google.adk.models.registry -  Updating LLM class for gemini-.* from <class 'google.adk.models.google_llm.Gemini'> to <class 'google.adk.models.google_llm.Gemini'>
2025/07/03 14:18:17 - INFO - google_adk.google.adk.models.registry -  Updating LLM class for projects\/.+\/locations\/.+\/endpoints\/.+ from <class 'google.adk.models.google_llm.Gemini'> to <class 'google.adk.models.google_llm.Gemini'>
2025/07/03 14:18:17 - INFO - google_adk.google.adk.models.registry -  Updating LLM class for projects\/.+\/locations\/.+\/publishers\/google\/models\/gemini.+ from <class 'google.adk.models.google_llm.Gemini'> to <class 'google.adk.models.google_llm.Gemini'>
2025/07/03 14:18:17 - INFO - google_adk.google.adk.models.registry -  Updating LLM class for gemini-.* from <class 'google.adk.models.google_llm.Gemini'> to <class 'google.adk.models.google_llm.Gemini'>
2025/07/03 14:18:17 - INFO - google_adk.google.adk.models.registry -  Updating LLM class for projects\/.+\/locations\/.+\/endpoints\/.+ from <class 'google.adk.models.google_llm.Gemini'> to <class 'google.adk.models.google_llm.Gemini'>
2025/07/03 14:18:17 - INFO - google_adk.google.adk.models.registry -  Updating LLM class for projects\/.+\/locations\/.+\/publishers\/google\/models\/gemini.+ from <class 'google.adk.models.google_llm.Gemini'> to <class 'google.adk.models.google_llm.Gemini'>
2025/07/03 14:18:19 - INFO - httpx -  HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
