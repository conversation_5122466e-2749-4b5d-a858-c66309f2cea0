2025/07/03 14:18:26 - INFO - google_adk.google.adk.models.registry -  Updating LLM class for gemini-.* from <class 'google.adk.models.google_llm.Gemini'> to <class 'google.adk.models.google_llm.Gemini'>
2025/07/03 14:18:26 - INFO - google_adk.google.adk.models.registry -  Updating LLM class for projects\/.+\/locations\/.+\/endpoints\/.+ from <class 'google.adk.models.google_llm.Gemini'> to <class 'google.adk.models.google_llm.Gemini'>
2025/07/03 14:18:26 - INFO - google_adk.google.adk.models.registry -  Updating LLM class for projects\/.+\/locations\/.+\/publishers\/google\/models\/gemini.+ from <class 'google.adk.models.google_llm.Gemini'> to <class 'google.adk.models.google_llm.Gemini'>
2025/07/03 14:18:26 - INFO - google_adk.google.adk.models.registry -  Updating LLM class for gemini-.* from <class 'google.adk.models.google_llm.Gemini'> to <class 'google.adk.models.google_llm.Gemini'>
2025/07/03 14:18:26 - INFO - google_adk.google.adk.models.registry -  Updating LLM class for projects\/.+\/locations\/.+\/endpoints\/.+ from <class 'google.adk.models.google_llm.Gemini'> to <class 'google.adk.models.google_llm.Gemini'>
2025/07/03 14:18:26 - INFO - google_adk.google.adk.models.registry -  Updating LLM class for projects\/.+\/locations\/.+\/publishers\/google\/models\/gemini.+ from <class 'google.adk.models.google_llm.Gemini'> to <class 'google.adk.models.google_llm.Gemini'>
2025/07/03 14:18:28 - INFO - httpx -  HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025/07/03 14:18:29 - INFO - root -  使用的模型供应商是: openrouter，模型是: openrouter/google/gemini-2.5-pro
