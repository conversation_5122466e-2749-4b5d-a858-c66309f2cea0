"use client";
import { useState } from "react";
import { Download, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { usePresentationState } from "@/states/presentation-state";
import { useParams } from "next/navigation";

export function ExportButton() {
  const [isExporting, setIsExporting] = useState(false);
  const params = useParams();
  const id = params.id as string;
  const { currentPresentationTitle } = usePresentationState();

  const handleExport = async () => {
    if (!id) {
      toast.error("无法导出：未找到演示文稿ID");
      return;
    }

    setIsExporting(true);
    
    try {
      // 调用导出API
      const response = await fetch(`/api/presentation/export/${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('导出失败');
      }

      // 获取文件blob
      const blob = await response.blob();
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `${currentPresentationTitle || 'presentation'}.pptx`;
      
      // 触发下载
      document.body.appendChild(a);
      a.click();
      
      // 清理
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success("PPT导出成功！");
    } catch (error) {
      console.error('导出错误:', error);
      toast.error("导出失败，请稍后重试");
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      className="text-muted-foreground hover:text-foreground"
      onClick={handleExport}
      disabled={isExporting}
    >
      {isExporting ? (
        <Loader2 className="mr-1 h-4 w-4 animate-spin" />
      ) : (
        <Download className="mr-1 h-4 w-4" />
      )}
      {isExporting ? "导出中..." : "导出PPT"}
    </Button>
  );
}
